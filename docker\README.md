# 🐳 Webscout Docker Deployment Guide

This directory contains Docker configurations for deploying the Webscout API server in various environments.

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Configuration](#configuration)
- [Deployment Scenarios](#deployment-scenarios)
- [Security](#security)
- [Monitoring](#monitoring)
- [Troubleshooting](#troubleshooting)

## 🚀 Quick Start

### Basic Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# Or build and run manually
docker build -t webscout-api .
docker run -d -p 8000:8000 --name webscout-api webscout-api
```

### Check Status

```bash
# Check container status
docker-compose ps

# View logs
docker-compose logs -f webscout-api

# Check health
curl http://localhost:8000/v1/models
```

## ⚙️ Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `WEBSCOUT_HOST` | `0.0.0.0` | Server bind address |
| `WEBSCOUT_PORT` | `8000` | Server port |
| `WEBSCOUT_WORKERS` | `1` | Number of worker processes |
| `WEBSCOUT_LOG_LEVEL` | `info` | Logging level |
| `WEBSCOUT_API_KEY` | - | API authentication key |
| `WEBSCOUT_DEFAULT_PROVIDER` | `ChatGPT` | Default LLM provider |
| `WEBSCOUT_BASE_URL` | - | Base URL path |
| `WEBSCOUT_DEBUG` | `false` | Enable debug mode |

### Docker Compose Override

Create `docker-compose.override.yml` for local customizations:

```yaml
version: '3.8'
services:
  webscout-api:
    environment:
      - WEBSCOUT_API_KEY=your-secret-key
      - WEBSCOUT_DEFAULT_PROVIDER=ChatGPT
    ports:
      - "8080:8000"  # Use different port
```

## 🎯 Deployment Scenarios

### Development

```bash
# Start development environment with hot reload
docker-compose --profile development up -d
```

Features:
- Source code mounting for live updates
- Debug logging enabled
- Single worker for easier debugging

### Production

```bash
# Start production environment
docker-compose --profile production up -d
```

Features:
- Multiple workers for better performance
- Gunicorn WSGI server
- Resource limits and health checks
- Optimized logging

### With Nginx Reverse Proxy

```bash
# Start with Nginx proxy
docker-compose --profile nginx up -d
```

Features:
- SSL termination
- Load balancing
- Static file serving
- Rate limiting

### With Monitoring

```bash
# Start with Prometheus monitoring
docker-compose --profile monitoring up -d
```

Access monitoring at:
- Prometheus: http://localhost:9090

## 🔒 Security

### Best Practices

1. **Use API Keys**: Always set `WEBSCOUT_API_KEY` in production
2. **Non-root User**: Container runs as non-root user `webscout`
3. **Read-only Filesystem**: Enable for maximum security
4. **Resource Limits**: Set memory and CPU limits
5. **Network Isolation**: Use custom networks

### Security Configuration

```yaml
services:
  webscout-api:
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /app/logs
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
```

### SSL/TLS Setup

1. Place certificates in `docker/ssl/`
2. Update `docker/nginx.conf`
3. Start with nginx profile

## 📊 Monitoring

### Health Checks

The container includes comprehensive health checks:

```bash
# Manual health check
docker exec webscout-api python /usr/local/bin/healthcheck.py

# View health status
docker inspect --format='{{.State.Health.Status}}' webscout-api
```

### Logs

```bash
# Application logs
docker-compose logs -f webscout-api

# Access logs (with nginx)
docker-compose logs -f nginx

# System logs
docker exec webscout-api tail -f /app/logs/webscout.log
```

### Metrics

With Prometheus monitoring enabled:

- Container metrics via cAdvisor
- Application metrics via custom endpoints
- Health check metrics

## 🔧 Troubleshooting

### Common Issues

#### Container Won't Start

```bash
# Check logs
docker-compose logs webscout-api

# Check configuration
docker-compose config

# Validate environment
docker exec webscout-api env | grep WEBSCOUT
```

#### Health Check Failures

```bash
# Run health check manually
docker exec webscout-api python /usr/local/bin/healthcheck.py

# Check API endpoints
curl -v http://localhost:8000/v1/models

# Check container resources
docker stats webscout-api
```

#### Permission Issues

```bash
# Check file permissions
docker exec webscout-api ls -la /app

# Fix ownership (if needed)
docker exec --user root webscout-api chown -R webscout:webscout /app
```

#### Network Issues

```bash
# Check port binding
docker port webscout-api

# Test connectivity
docker exec webscout-api curl -v http://localhost:8000/v1/models

# Check network configuration
docker network ls
docker network inspect webscout-network
```

### Debug Mode

Enable debug mode for detailed logging:

```bash
# Set debug environment
export WEBSCOUT_DEBUG=true
docker-compose up -d

# Or override in compose
docker-compose -f docker-compose.yml -f docker-compose.debug.yml up -d
```

### Performance Tuning

#### Memory Optimization

```yaml
environment:
  - MALLOC_ARENA_MAX=2  # Reduce memory fragmentation
  - PYTHONHASHSEED=random  # Security
deploy:
  resources:
    limits:
      memory: 1G
    reservations:
      memory: 512M
```

#### Worker Configuration

```bash
# Calculate optimal workers
# Formula: (2 x CPU cores) + 1
export WEBSCOUT_WORKERS=$(($(nproc) * 2 + 1))
```

### Backup and Recovery

```bash
# Backup volumes
docker run --rm -v webscout-data:/data -v $(pwd):/backup alpine tar czf /backup/webscout-data.tar.gz -C /data .

# Restore volumes
docker run --rm -v webscout-data:/data -v $(pwd):/backup alpine tar xzf /backup/webscout-data.tar.gz -C /data
```

## 📚 Additional Resources

- [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)
- [Docker Compose Reference](https://docs.docker.com/compose/compose-file/)
- [Container Security](https://docs.docker.com/engine/security/)
- [Webscout Documentation](../README.md)

## 🆘 Support

If you encounter issues:

1. Check the [troubleshooting section](#troubleshooting)
2. Review container logs
3. Verify configuration
4. Open an issue on GitHub with:
   - Docker version
   - Compose file
   - Error logs
   - Environment details
