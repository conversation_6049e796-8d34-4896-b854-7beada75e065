#!/usr/bin/env python3
"""
Webscout API Server Health Check Script
Performs comprehensive health checks for the API server
"""

import os
import sys
import time
import json
import urllib.request
import urllib.error
from typing import Dict, Any, Optional


class HealthChecker:
    """Health check implementation for Webscout API server."""
    
    def __init__(self):
        self.host = os.getenv('WEBSCOUT_HOST', '0.0.0.0')
        self.port = int(os.getenv('WEBSCOUT_PORT', '8000'))
        self.timeout = int(os.getenv('HEALTHCHECK_TIMEOUT', '10'))
        self.api_key = os.getenv('WEBSCOUT_API_KEY')
        
        # Use localhost for health checks when binding to 0.0.0.0
        self.check_host = 'localhost' if self.host == '0.0.0.0' else self.host
        self.base_url = f"http://{self.check_host}:{self.port}"
    
    def log(self, level: str, message: str) -> None:
        """Log a message with timestamp."""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] [{level}] {message}", file=sys.stderr)
    
    def make_request(self, endpoint: str, headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """Make an HTTP request to the API server."""
        url = f"{self.base_url}{endpoint}"
        
        if headers is None:
            headers = {}
        
        # Add API key if configured
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'
        
        headers['User-Agent'] = 'Webscout-HealthCheck/1.0'
        
        try:
            req = urllib.request.Request(url, headers=headers)
            with urllib.request.urlopen(req, timeout=self.timeout) as response:
                status_code = response.getcode()
                content_type = response.headers.get('content-type', '')
                
                if 'application/json' in content_type:
                    data = json.loads(response.read().decode('utf-8'))
                else:
                    data = response.read().decode('utf-8')
                
                return {
                    'status_code': status_code,
                    'data': data,
                    'headers': dict(response.headers)
                }
        
        except urllib.error.HTTPError as e:
            return {
                'status_code': e.code,
                'error': f'HTTP {e.code}: {e.reason}',
                'data': None
            }
        except urllib.error.URLError as e:
            return {
                'status_code': 0,
                'error': f'Connection error: {e.reason}',
                'data': None
            }
        except Exception as e:
            return {
                'status_code': 0,
                'error': f'Unexpected error: {str(e)}',
                'data': None
            }
    
    def check_basic_connectivity(self) -> bool:
        """Check if the server is responding to basic requests."""
        self.log('INFO', f'Checking basic connectivity to {self.base_url}')
        
        response = self.make_request('/')
        
        if response['status_code'] in [200, 307, 308]:  # Include redirects as healthy
            self.log('INFO', 'Basic connectivity check passed')
            return True
        else:
            self.log('ERROR', f'Basic connectivity failed: {response.get("error", "Unknown error")}')
            return False
    
    def check_models_endpoint(self) -> bool:
        """Check if the models endpoint is working."""
        self.log('INFO', 'Checking models endpoint')
        
        response = self.make_request('/v1/models')
        
        if response['status_code'] == 200:
            data = response.get('data')
            if isinstance(data, dict) and 'data' in data:
                model_count = len(data['data'])
                self.log('INFO', f'Models endpoint check passed ({model_count} models available)')
                return True
            else:
                self.log('ERROR', 'Models endpoint returned invalid data format')
                return False
        else:
            self.log('ERROR', f'Models endpoint failed: {response.get("error", "Unknown error")}')
            return False
    
    def check_health_endpoint(self) -> bool:
        """Check if there's a dedicated health endpoint."""
        self.log('INFO', 'Checking health endpoint')
        
        response = self.make_request('/health')
        
        if response['status_code'] == 200:
            self.log('INFO', 'Health endpoint check passed')
            return True
        elif response['status_code'] == 404:
            self.log('INFO', 'Health endpoint not found (not critical)')
            return True  # Not critical if health endpoint doesn't exist
        else:
            self.log('WARN', f'Health endpoint check failed: {response.get("error", "Unknown error")}')
            return True  # Not critical
    
    def check_api_functionality(self) -> bool:
        """Check if the API can handle a basic chat completion request."""
        self.log('INFO', 'Checking API functionality with test request')
        
        # Prepare a minimal test request
        test_data = {
            "model": "SonusAI/mini",
            "messages": [{"role": "user", "content": "Hello"}],
            "max_tokens": 1,
            "stream": False
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        try:
            # Create request with JSON data
            url = f"{self.base_url}/v1/chat/completions"
            data = json.dumps(test_data).encode('utf-8')
            
            if self.api_key:
                headers['Authorization'] = f'Bearer {self.api_key}'
            
            req = urllib.request.Request(url, data=data, headers=headers, method='POST')
            
            with urllib.request.urlopen(req, timeout=self.timeout) as response:
                status_code = response.getcode()
                
                if status_code in [200, 400, 401, 403]:  # Accept auth errors as "functional"
                    self.log('INFO', 'API functionality check passed')
                    return True
                else:
                    self.log('ERROR', f'API functionality check failed with status {status_code}')
                    return False
        
        except Exception as e:
            self.log('ERROR', f'API functionality check failed: {str(e)}')
            return False
    
    def run_health_check(self) -> bool:
        """Run all health checks and return overall status."""
        self.log('INFO', 'Starting health check...')
        
        checks = [
            ('Basic Connectivity', self.check_basic_connectivity),
            ('Models Endpoint', self.check_models_endpoint),
            ('Health Endpoint', self.check_health_endpoint),
            ('API Functionality', self.check_api_functionality),
        ]
        
        results = []
        for check_name, check_func in checks:
            try:
                result = check_func()
                results.append(result)
                status = 'PASS' if result else 'FAIL'
                self.log('INFO', f'{check_name}: {status}')
            except Exception as e:
                self.log('ERROR', f'{check_name}: FAIL ({str(e)})')
                results.append(False)
        
        # Determine overall health
        critical_checks = results[:2]  # Basic connectivity and models endpoint are critical
        overall_healthy = all(critical_checks)
        
        if overall_healthy:
            self.log('INFO', 'Health check completed: HEALTHY')
        else:
            self.log('ERROR', 'Health check completed: UNHEALTHY')
        
        return overall_healthy


def main():
    """Main health check entry point."""
    try:
        checker = HealthChecker()
        healthy = checker.run_health_check()
        
        if healthy:
            print("HEALTHY")
            sys.exit(0)
        else:
            print("UNHEALTHY")
            sys.exit(1)
    
    except Exception as e:
        print(f"HEALTH CHECK ERROR: {str(e)}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()
