#!/bin/bash
# =============================================================================
# Webscout API Server Entrypoint Script
# Provides flexible startup options and environment configuration
# =============================================================================

set -euo pipefail

# Default values
DEFAULT_HOST="${WEBSCOUT_HOST:-0.0.0.0}"
DEFAULT_PORT="${WEBSCOUT_PORT:-8000}"
DEFAULT_WORKERS="${WEBSCOUT_WORKERS:-1}"
DEFAULT_LOG_LEVEL="${WEBSCOUT_LOG_LEVEL:-info}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [[ "${WEBSCOUT_DEBUG:-false}" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# Function to wait for dependencies (if needed)
wait_for_dependencies() {
    log_info "Checking dependencies..."
    
    # Add any dependency checks here
    # Example: wait for database, redis, etc.
    
    log_info "Dependencies check completed"
}

# Function to validate environment
validate_environment() {
    log_info "Validating environment configuration..."
    
    # Validate port
    if ! [[ "$DEFAULT_PORT" =~ ^[0-9]+$ ]] || [ "$DEFAULT_PORT" -lt 1 ] || [ "$DEFAULT_PORT" -gt 65535 ]; then
        log_error "Invalid port: $DEFAULT_PORT. Must be between 1-65535"
        exit 1
    fi
    
    # Validate workers
    if ! [[ "$DEFAULT_WORKERS" =~ ^[0-9]+$ ]] || [ "$DEFAULT_WORKERS" -lt 1 ]; then
        log_error "Invalid workers count: $DEFAULT_WORKERS. Must be >= 1"
        exit 1
    fi
    
    # Validate log level
    case "$DEFAULT_LOG_LEVEL" in
        debug|info|warning|error|critical)
            ;;
        *)
            log_error "Invalid log level: $DEFAULT_LOG_LEVEL. Must be one of: debug, info, warning, error, critical"
            exit 1
            ;;
    esac
    
    log_info "Environment validation completed"
}

# Function to setup logging
setup_logging() {
    log_info "Setting up logging..."
    
    # Create logs directory if it doesn't exist
    mkdir -p /app/logs
    
    # Set log file permissions
    touch /app/logs/webscout.log
    chmod 644 /app/logs/webscout.log
    
    log_info "Logging setup completed"
}

# Function to display startup information
display_startup_info() {
    log_info "Starting Webscout API Server..."
    log_info "Configuration:"
    log_info "  Host: $DEFAULT_HOST"
    log_info "  Port: $DEFAULT_PORT"
    log_info "  Workers: $DEFAULT_WORKERS"
    log_info "  Log Level: $DEFAULT_LOG_LEVEL"
    log_info "  Python Version: $(python --version)"
    log_info "  Working Directory: $(pwd)"
    
    if [[ "${WEBSCOUT_API_KEY:-}" ]]; then
        log_info "  API Key: *** (configured)"
    else
        log_warn "  API Key: Not configured (authentication disabled)"
    fi
    
    if [[ "${WEBSCOUT_DEFAULT_PROVIDER:-}" ]]; then
        log_info "  Default Provider: $WEBSCOUT_DEFAULT_PROVIDER"
    fi
}

# Function to start the server
start_server() {
    local cmd="$1"
    shift
    
    case "$cmd" in
        "webscout-server"|"server")
            log_info "Starting Webscout API server..."
            exec python -m webscout.Provider.OPENAI.api \
                --port "$DEFAULT_PORT" \
                ${WEBSCOUT_API_KEY:+--api-key "$WEBSCOUT_API_KEY"} \
                ${WEBSCOUT_DEFAULT_PROVIDER:+--default-provider "$WEBSCOUT_DEFAULT_PROVIDER"} \
                ${WEBSCOUT_BASE_URL:+--base-url "$WEBSCOUT_BASE_URL"} \
                ${WEBSCOUT_DEBUG:+--debug}
            ;;
        "gunicorn")
            log_info "Starting with Gunicorn..."
            exec gunicorn \
                --bind "$DEFAULT_HOST:$DEFAULT_PORT" \
                --workers "$DEFAULT_WORKERS" \
                --worker-class uvicorn.workers.UvicornWorker \
                --log-level "$DEFAULT_LOG_LEVEL" \
                --access-logfile /app/logs/access.log \
                --error-logfile /app/logs/error.log \
                --preload \
                webscout.Provider.OPENAI.api:create_app
            ;;
        "uvicorn")
            log_info "Starting with Uvicorn..."
            exec uvicorn \
                --host "$DEFAULT_HOST" \
                --port "$DEFAULT_PORT" \
                --workers "$DEFAULT_WORKERS" \
                --log-level "$DEFAULT_LOG_LEVEL" \
                --access-log \
                webscout.Provider.OPENAI.api:create_app
            ;;
        "bash"|"sh")
            log_info "Starting interactive shell..."
            exec "$cmd"
            ;;
        *)
            log_info "Executing custom command: $cmd $*"
            exec "$cmd" "$@"
            ;;
    esac
}

# Main execution
main() {
    log_info "Webscout API Server Entrypoint"
    log_info "=============================="
    
    # Setup
    validate_environment
    setup_logging
    wait_for_dependencies
    display_startup_info
    
    # Handle different startup modes
    if [ $# -eq 0 ]; then
        # No arguments, use default
        start_server "webscout-server"
    else
        # Arguments provided
        start_server "$@"
    fi
}

# Handle signals for graceful shutdown
trap 'log_info "Received shutdown signal, stopping..."; exit 0' SIGTERM SIGINT

# Run main function
main "$@"
