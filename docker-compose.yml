# =============================================================================
# Docker Compose Configuration for Webscout API Server
# Provides multiple deployment scenarios and configurations
# =============================================================================

version: '3.8'

services:
  # -----------------------------------------------------------------------------
  # Main Webscout API Server
  # -----------------------------------------------------------------------------
  webscout-api:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - WEBSCOUT_VERSION=latest
        - BUILD_DATE=${BUILD_DATE:-}
        - VCS_REF=${VCS_REF:-}
        - VERSION=${VERSION:-latest}
    image: webscout-api:latest
    container_name: webscout-api
    restart: unless-stopped
    
    # Environment configuration
    environment:
      # Server settings
      - WEBSCOUT_HOST=0.0.0.0
      - WEBSCOUT_PORT=8000
      - WEBSCOUT_WORKERS=1
      - WEBSCOUT_LOG_LEVEL=info
      
      # Optional API configuration
      # - WEBSCOUT_API_KEY=your-secret-api-key
      # - WEBSCOUT_DEFAULT_PROVIDER=ChatGPT
      # - WEBSCOUT_BASE_URL=/api/v1
      
      # Debug mode (set to true for development)
      - WEBSCOUT_DEBUG=false
      
      # Health check settings
      - HEALTHCHECK_TIMEOUT=10
    
    # Port mapping
    ports:
      - "8000:8000"
    
    # Volume mounts for persistence
    volumes:
      - webscout-logs:/app/logs
      - webscout-data:/app/data
    
    # Health check configuration
    healthcheck:
      test: ["CMD", "python", "/usr/local/bin/healthcheck.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    
    # Security settings
    security_opt:
      - no-new-privileges:true
    read_only: false  # Set to true for maximum security, but may need adjustments
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # -----------------------------------------------------------------------------
  # Production setup with Gunicorn (alternative configuration)
  # -----------------------------------------------------------------------------
  webscout-api-production:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - WEBSCOUT_VERSION=latest
    image: webscout-api:latest
    container_name: webscout-api-prod
    restart: unless-stopped
    profiles:
      - production
    
    environment:
      - WEBSCOUT_HOST=0.0.0.0
      - WEBSCOUT_PORT=8000
      - WEBSCOUT_WORKERS=4  # More workers for production
      - WEBSCOUT_LOG_LEVEL=warning
      - WEBSCOUT_DEBUG=false
    
    ports:
      - "8000:8000"
    
    volumes:
      - webscout-logs:/app/logs
      - webscout-data:/app/data
    
    command: ["gunicorn"]  # Use Gunicorn for production
    
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'

  # -----------------------------------------------------------------------------
  # Development setup with hot reload
  # -----------------------------------------------------------------------------
  webscout-api-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder  # Use builder stage for development
      args:
        - WEBSCOUT_VERSION=latest
    image: webscout-api:dev
    container_name: webscout-api-dev
    restart: unless-stopped
    profiles:
      - development
    
    environment:
      - WEBSCOUT_HOST=0.0.0.0
      - WEBSCOUT_PORT=8000
      - WEBSCOUT_WORKERS=1
      - WEBSCOUT_LOG_LEVEL=debug
      - WEBSCOUT_DEBUG=true
    
    ports:
      - "8000:8000"
    
    volumes:
      - .:/app  # Mount source code for development
      - webscout-logs:/app/logs
      - webscout-data:/app/data
    
    command: ["uvicorn"]  # Use Uvicorn with auto-reload for development

  # -----------------------------------------------------------------------------
  # Nginx reverse proxy (optional)
  # -----------------------------------------------------------------------------
  nginx:
    image: nginx:alpine
    container_name: webscout-nginx
    restart: unless-stopped
    profiles:
      - nginx
      - production
    
    ports:
      - "80:80"
      - "443:443"
    
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
      - webscout-logs:/var/log/nginx
    
    depends_on:
      - webscout-api
    
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # -----------------------------------------------------------------------------
  # Monitoring with Prometheus (optional)
  # -----------------------------------------------------------------------------
  prometheus:
    image: prom/prometheus:latest
    container_name: webscout-prometheus
    restart: unless-stopped
    profiles:
      - monitoring
    
    ports:
      - "9090:9090"
    
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

# =============================================================================
# Named volumes for data persistence
# =============================================================================
volumes:
  webscout-logs:
    driver: local
  webscout-data:
    driver: local
  prometheus-data:
    driver: local

# =============================================================================
# Networks (optional, for advanced setups)
# =============================================================================
networks:
  default:
    name: webscout-network
    driver: bridge
