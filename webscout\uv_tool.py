"""
UV tool compatibility module.

This module provides functions and utilities for making webscout compatible with the uv tool.
"""

import sys
import os
from .cli import main as cli_main
from .client import start_server

def install():
    """
    Install entry point for uv tool.
    
    This function is called when running `uvx install webscout`.
    """
    print("Installing webscout...")
    # Perform any installation-specific tasks here
    print("webscout installed successfully!")
    return 0

def run():
    """
    Run entry point for uv tool.
    
    This function is called when running `uvx webscout`.
    """
    return cli_main()

def server():
    """
    Server entry point for uv tool.
    
    This function is called when running `uvx webscout-server`.
    """
    return start_server()

if __name__ == "__main__":
    # Determine which command to run based on the command name
    command = os.path.basename(sys.argv[0])
    
    if command == "install":
        sys.exit(install())
    elif command == "webscout-server":
        sys.exit(server())
    else:
        sys.exit(run())