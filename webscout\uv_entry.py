"""
UV tool compatibility entry point.

This module provides entry points for the uv tool to properly install and run webscout.
"""

from webscout.cli import main as cli_main
from webscout.client import start_server

def main():
    """Main entry point for uv tool."""
    return cli_main()

def server():
    """Server entry point for uv tool."""
    return start_server()

if __name__ == "__main__":
    main()